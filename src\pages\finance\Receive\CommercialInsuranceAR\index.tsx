import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, Typography } from 'antd';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { stdDateFormat } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import BillCreatorSubSelectPop from '../QueryBill/components/BillCreatorSubSelectPop';
import { invoiceStatusType, verifyStatusType } from '@/utils/settings/finance/queryExBill';
import { BillDetailModal } from './BillDetailModal';
import NumericInput from '@/pages/Sales/Quotation/QuotationTempManage/NumericInput';
import { formatAmount } from '@/utils/methods/format';

const CommercialInsuranceAR = () => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [_processInstanceId, _setProcessInstanceId] = useState<string>();

  // 表单配置
  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => (
        <CustomerPop
          rowValue="custId-custName"
          keyMap={{ custId: 'custId', custName: 'custName' }}
        />
      ),
    },
    {
      label: '账单年月',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '账单年月起始月', dataIndex: 'billYmSt' },
            { title: '账单年月截止月', dataIndex: 'billYmEd' },
          ]}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '财务应收年月',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '财务应收起始年月', dataIndex: 'finReceivableYmSt' },
            { title: '财务应收截止年月', dataIndex: 'finReceivableYmEd' },
          ]}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '应收金额>=',
      fieldName: 'receivableAmtSt',
      inputRender: () => (
        <NumericInput
          maxDecimalPlaces={2}
          allowNegative={false}
          min={0}
          placeholder="请输入应收金额起始值"
          style={{ width: '100%' }}
        />
      ),
      rules: [
        {
          validator: (_, value) => {
            if (!value) return Promise.resolve();
            const numValue = Number(value);
            if (isNaN(numValue) || numValue < 0) {
              return Promise.reject(new Error('请输入有效的应收金额数字'));
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      label: '生成人',
      fieldName: 'createBy',
      inputRender: () => (
        <BillCreatorSubSelectPop
          rowValue="RN-EMPID-REALNAME"
          keyMap={{
            RN: 'RN',
            creater: 'EMPID',
            REALNAME: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '应收金额<=',
      fieldName: 'receivableAmtEd',
      inputRender: () => (
        <NumericInput
          maxDecimalPlaces={2}
          allowNegative={false}
          min={0}
          placeholder="请输入应收金额截止值"
          style={{ width: '100%' }}
        />
      ),
      rules: [
        {
          validator: (_, value) => {
            if (!value) return Promise.resolve();
            const numValue = Number(value);
            if (isNaN(numValue) || numValue < 0) {
              return Promise.reject(new Error('请输入有效的应收金额数字'));
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      label: '核销状态',
      fieldName: 'verifyStatus',
      inputRender: () => mapToSelectors(verifyStatusType, { allowClear: true }),
    },
    {
      label: '开票状态',
      fieldName: 'invoiceStatus',
      inputRender: () => mapToSelectors(invoiceStatusType, { allowClear: true }),
    },
    {
      label: '同步时间>=',
      fieldName: 'createDtSt',
      inputRender: 'date',
    },
    {
      label: '同步时间<=',
      fieldName: 'createDtEd',
      inputRender: 'date',
    },
  ];

  // 表格列配置
  const columns: WritableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '账单名称', dataIndex: 'contractCode' },
    { title: '签单分公司抬头名称', dataIndex: 'contractName' },
    { title: '账单年月', dataIndex: 'signProvider' },
    { title: '财务应收年月', dataIndex: 'contractTypeName' },
    {
      title: '应收金额',
      dataIndex: 'receivableAmt',
      render: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return '-';
        }
        // 确保值是数字类型，并格式化为两位小数
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return value;
        }
        return <Typography.Link>{numValue.toFixed(2)}</Typography.Link>;
      },
    },
    { title: '生成人', dataIndex: 'contractSubTypeName' },
    { title: '同步时间', dataIndex: 'contractStartDate' },
    { title: '核销状态', dataIndex: 'liabilityCsName' },
    { title: '开票状态', dataIndex: 'contractStopDate' },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AsyncButton
        onClick={() => {
          options.handleExport(
            { service: API.sale.workPlaceHealth.toDownLoad },
            {
              columns,
              condition: { ...options.queries },
              fileName: '职场健康查询结果.xlsx',
            },
          );
        }}
      >
        导出
      </AsyncButton>
    </>
  );

  return (
    <>
      <CachedPage
        service={API.sale.workPlaceHealth.selectList}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        notShowRowSelection
      />
      <BillDetailModal modal={[visible, setVisible]} />
    </>
  );
};

export default CommercialInsuranceAR;
