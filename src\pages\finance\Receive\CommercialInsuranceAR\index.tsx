import React, { useState, useCallback, useMemo } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance } from 'antd';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { QueryApproveProcessWin } from '@/pages/Sales/Contract/Manage/QueryApproveProcessWin';
import { contractStatusMap, contractSvcStateMap } from '@/utils/settings/sales/contract';
import { stdDateFormat } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import {
  ContractSubTypeSelector,
  ContractTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import BillCreatorSubSelectPop from '../QueryBill/components/BillCreatorSubSelectPop';
import { invoiceStatusType, verifyStatusType } from '@/utils/settings/finance/queryExBill';
import { BillDetailModal } from './BillDetailModal';
import NumericInput from '@/pages/Sales/Quotation/QuotationTempManage/NumericInput';
import { formatAmount } from '@/utils/methods/format';

// 服务配置 - 提取到组件外部避免重复创建
const SERVICES = {
  query: API.sale.workPlaceHealth.selectList,
  export: API.sale.workPlaceHealth.toDownLoad,
} as const;

// 导出文件名配置
const EXPORT_CONFIG = {
  queryResult: '职场健康查询结果.xlsx',
  report: '职场健康相关数据报表.xlsx',
} as const;

// 日期范围配置 - 提取公共配置
const DATE_RANGE_CONFIGS = {
  billYm: {
    label: '账单年月',
    fields: [
      { title: '账单年月起始月', dataIndex: 'billYmSt' },
      { title: '账单年月截止月', dataIndex: 'billYmEd' },
    ],
  },
  finReceivableYm: {
    label: '财务应收',
    fields: [
      { title: '财务应收起始年月', dataIndex: 'finReceivableYmSt' },
      { title: '财务应收截止年月', dataIndex: 'finReceivableYmEd' },
    ],
  },
};

// 客户弹窗配置
const CUSTOMER_POP_CONFIG = {
  rowValue: 'custId-custName',
  keyMap: { custId: 'custId', custName: 'custName' },
} as const;

const CommercialInsuranceAR = () => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [processInstanceId, setProcessInstanceId] = useState<string>();

  // 创建日期范围组件 - 复用逻辑
  const createDateRange = useCallback(
    (config: any) => (
      <DateRange
        fields={config.fields.map((field: any) => ({
          ...field,
          ...((field as any).required && {
            rules: [{ required: true, message: `请选择${field.title}` }],
          }),
        }))}
        format={stdDateFormat}
        {...(config.range && { range: config.range })}
      />
    ),
    [],
  );
  // 表单配置 - 使用 useMemo 优化性能
  const formColumns: EditeFormProps[] = useMemo(
    () => [
      {
        label: '客户',
        fieldName: 'custId',
        inputRender: () => <CustomerPop {...CUSTOMER_POP_CONFIG} />,
      },
      {
        label: DATE_RANGE_CONFIGS.billYm.label,
        fieldName: '',
        inputRender: () => createDateRange(DATE_RANGE_CONFIGS.billYm),
      },
      {
        label: DATE_RANGE_CONFIGS.finReceivableYm.label,
        fieldName: '',
        inputRender: () => createDateRange(DATE_RANGE_CONFIGS.finReceivableYm),
      },
      {
        label: '应收金额>=',
        fieldName: 'receivableAmtSt',
        inputRender: () => (
          <NumericInput
            maxDecimalPlaces={2}
            allowNegative={false}
            min={0}
            placeholder="请输入应收金额起始值"
            style={{ width: '100%' }}
          />
        ),
        rules: [
          {
            validator: (_, value) => {
              if (!value) return Promise.resolve();
              const numValue = Number(value);
              if (isNaN(numValue) || numValue < 0) {
                return Promise.reject(new Error('请输入有效的应收金额数字'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      {
        label: '生成人',
        fieldName: 'createBy',
        inputRender: () => {
          return (
            <BillCreatorSubSelectPop
              rowValue="RN-EMPID-REALNAME"
              keyMap={{
                RN: 'RN',
                creater: 'EMPID',
                REALNAME: 'REALNAME',
              }}
            />
          );
        },
      },
      {
        label: '应收金额<=',
        fieldName: 'receivableAmtEd',
        inputRender: () => (
          <NumericInput
            maxDecimalPlaces={2}
            allowNegative={false}
            min={0}
            placeholder="请输入应收金额截止值"
            style={{ width: '100%' }}
          />
        ),
        rules: [
          {
            validator: (_, value) => {
              if (!value) return Promise.resolve();
              const numValue = Number(value);
              if (isNaN(numValue) || numValue < 0) {
                return Promise.reject(new Error('请输入有效的应收金额数字'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      {
        label: '核销状态',
        fieldName: 'verifyStatus',
        inputRender: () => mapToSelectors(verifyStatusType, { allowClear: true }),
      },
      {
        label: '开票状态',
        fieldName: 'invoiceStatus',
        inputRender: () => mapToSelectors(invoiceStatusType, { allowClear: true }),
      },

      {
        label: '同步时间>=',
        fieldName: 'createDtSt',
        inputRender: 'date',
      },
      {
        label: '同步时间<=',
        fieldName: 'createDtEd',
        inputRender: 'date',
      },
    ],
    [createDateRange],
  );

  // 审批过程处理函数
  const handleApproveProcess = useCallback((processInstanceId: string) => {
    setVisible(true);
    setProcessInstanceId(processInstanceId);
  }, []);

  // 表格列配置 - 使用 useMemo 优化性能
  const columns: WritableColumnProps<any>[] = useMemo(
    () => [
      { title: '客户编号', dataIndex: 'custCode' },
      { title: '客户名称', dataIndex: 'custName' },
      { title: '账单名称', dataIndex: 'contractCode' },
      { title: '签单分公司抬头名称', dataIndex: 'contractName' },
      { title: '账单年月', dataIndex: 'signProvider' },
      { title: '财务应收年月', dataIndex: 'contractTypeName' },
      {
        title: '应收金额',
        dataIndex: 'receivableAmt',
        render: (value: any) => {
          if (value === null || value === undefined || value === '') {
            return '-';
          }
          // 确保值是数字类型，并格式化为两位小数
          const numValue = Number(value);
          if (isNaN(numValue)) {
            return value;
          }
          return formatAmount(numValue.toFixed(2));
        },
      },
      { title: '生成人', dataIndex: 'contractSubTypeName' },
      { title: '同步时间', dataIndex: 'contractStartDate' },
      { title: '核销状态', dataIndex: 'liabilityCsName' },
      { title: '开票状态', dataIndex: 'contractStopDate' }
    ],
    [handleApproveProcess],
  );

  const renderButtons = useCallback(
    (options: WritableInstance) => (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AsyncButton
          onClick={() => {
            options.handleExport(
              { service: SERVICES.export },
              {
                columns,
                condition: { ...options.queries },
                fileName: EXPORT_CONFIG.queryResult,
              },
            );
          }}
        >
          导出
        </AsyncButton>
      </>
    ),
    [columns],
  );

  return (
    <>
      <CachedPage
        service={SERVICES.query}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        notShowRowSelection
      />
      <BillDetailModal modal={[visible, setVisible]} />
    </>
  );
};

export default CommercialInsuranceAR;
