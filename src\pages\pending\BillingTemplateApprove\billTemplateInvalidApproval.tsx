import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableInstance } from '@/components/Writable';
import { isEmpty } from '@/utils/methods/checker';
import { msgErr, msgOk } from '@/utils/methods/message';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button } from 'antd';
import React, { useState } from 'react';
import { getUserId } from '@/utils/model';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import AddForm from '@/components/EditeForm/AddForm';

let _options: WritableInstance | undefined;
const BillTemplateInvalidApproval = () => {
  const service = API.emphiresep.receivable.queryTemplateDelPage;
  const [onShowBack, setOnShowBack] = useState<boolean>(false);
  const userId = getUserId();

  const columns: WritableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '账单模板名称', dataIndex: 'receivableTempltName' },
    { title: '帐单模板编号', dataIndex: 'receivableTempltId' },
    { title: '大合同编号', dataIndex: 'contractCode' },
    { title: '大合同名称', dataIndex: 'contractName' },
    { title: '申请人', dataIndex: 'realName' },
    { title: '申请时间', dataIndex: 'applyDt' },
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custName',
      inputRender: () => {
        return (
          <CustomerPop
            rowValue="custId-custName"
            keyMap={{
              custId: 'custId',
              custName: 'custName',
            }}
          />
        );
      },
    },
    { label: '帐单模板名称', fieldName: 'receivableTempltName', inputRender: 'string' },
    {
      label: '帐单模板编号',
      fieldName: 'receivableTempltId',
      inputRender: 'string',
      rules: [{ pattern: /^\d+$|^\d+[.]?\d+$/, message: '仅能输入数字查询' }],
    },
    {
      label: '申请人',
      fieldName: 'applicant',
      inputRender: () => {
        return (
          <InnerUserPop
            rowValue="applicant-realName"
            keyMap={{ applicant: 'USERID', realName: 'REALNAME' }}
          />
        );
      },
    },
  ];

  const formColumnsBack: EditeFormProps[] = [
    {
      label: '驳回原因',
      fieldName: 'backOpinion',
      inputRender: 'text',
      colNumber: 1,
      rules: [{ required: true, message: '请填写驳回原因' }],
    },
  ];

  const onApproval = async () => {
    if (isEmpty(_options?.selectedSingleRow)) return msgErr('请先选择数据');
    await API.emphiresep.receivable.doApproveTemplateDelFlow.requests(_options?.selectedSingleRow);
    msgOk('操作成功');
    _options?.request();
  };

  const onBack = async () => {
    if (isEmpty(_options?.selectedSingleRow)) return msgErr('请先选择数据');
    setOnShowBack(true);
  };

  const back_clickHandler = async (value: POJO) => {
    await API.emphiresep.receivable.terminalTemplateDelFlow.requests({
      ..._options?.selectedSingleRow,
      ...value,
    });
    msgOk('操作成功');
    setOnShowBack(false);
    _options?.request();
  };

  const renderButtons = (options: WritableInstance) => {
    _options = options;
    return (
      <React.Fragment>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={onApproval}>审批通过</Button>
        <Button onClick={onBack}>驳回</Button>
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <CachedPage
        service={service}
        columns={columns}
        formColumns={formColumns}
        fixedValues={{ participant: userId }}
        renderButtons={renderButtons}
        editable={false}
        notShowRowSelection
      />
      <AddForm
        title="增员失败原因"
        formColumns={formColumnsBack}
        visible={onShowBack}
        width={800}
        hideHandle={() => setOnShowBack(false)}
        submitHandle={back_clickHandler}
      />
    </React.Fragment>
  );
};

export default BillTemplateInvalidApproval;
