import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpa/job/runJob
     * @desc 触发Rpa定时任务
触发Rpa定时任务
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.rpa.CommonResponse();
export const url = '/rhro-service-1.0/rpa/job/runJob:POST';
export const initialUrl = '/rhro-service-1.0/rpa/job/runJob';
export const cacheKey = '_rpa_job_runJob_POST';
export async function request(
  data: defs.rpa.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/runJob`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.rpa.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/runJob`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
