/*
 * @Author: zhujianghua
 * @Email: <EMAIL>
 * @Date: 2020-09-10 10:28:06
 * @LastAuthor: zhujianghua
 * @LastTime: 2020-09-18 10:44:10
 * @message:
 */
export const freezeStatusMap = new Map<number, string>([
  [0, '解冻'],
  [1, '冻结'],
]);

export const alllowMap = new Map<number, string>([
  [1, '核销金额'],
  [2, '津贴金额'],
  [3, '待遇金额'],
  [4, '部分核销部分商保'],
  [5, '商保金额'],
]);

export const statusMap = new Map<number, string>([
  [1, '有效'],
  [2, '作废'],
]);

export const payTypeMap = new Map<number, string>([
  [1, '现金'],
  [2, '支票'],
  [3, '贷记'],
]);

export const feeTypeMap = new Map<number, string>([
  [1, '正常收费'],
  [2, '一次性收费'],
]);

export const payTypeDataMap = new Map<number, string>([[13, '津贴支付']]);
export const payTypeDataMap2 = new Map<number, string>([[14, '待遇支付']]);

export const payObjectDataMap = new Map<number, string>([
  [1, '对公'],
  [2, '对私'],
]);

export const payMethodMap = new Map<number, string>([
  [1, '银行汇款'],
  [2, '转账支票'],
  [3, '现金'],
  [4, '其它'],
]);

export const parTypeMap = new Map<number, string>([
  [1, '专票'],
  [2, '普票'],
]);

export const receiptsTypeMap = new Map<string, string>([
  ['08', '其它发票类'],
  ['09', '其它扣除凭证'],
]);

export const balanceTypeMap = new Map<string, string>([
  ['01', '差额征税-全额开票'],
  ['02', '差额征税-差额开票'],
]);

export const parModeMap = new Map<number, string>([
  [1, '正常单据（含税）'],
  [2, '正常单据（不含税）'],
  [3, '差额（含税）'],
  [4, '差额（不含税）'],
]);

export const parModeMap2 = new Map<number, string>([
  [1, '正常单据（含税）'],
  [3, '差额（含税）'],
]);

export const invoiceTypeMap = new Map<number, string>([
  [1, '电子发票'],
  [2, '纸制发票'],
  [3, '全电票'],
]);

export const jsSysMap = new Map<number, string>([
  [0, '否'],
  [1, '是'],
]);

export const parStatusMap = new Map<number, string>([
  [1, '待审批'],
  [2, '成功'],
  [3, '手工'],
  [4, '作废'],
]);
