{"socialmanage.QuerySSLock": "赵煜灏 社保管理 社保锁定 | ./empwelfare/LockManage/Social", "socialmanage.report.SocialSecReport": "赵煜灏 社保管理 社保报表 | ./empwelfare/Report/SocialSecReport", "providentFund.QuerySSLock": "赵煜灏  公积金管理 公积金锁定 | ./empwelfare/LockManage/Provident", "providentFund.report.ProvidentFunReport": "赵煜灏 公积金管理 公积金报表 | ./empwelfare/Report/ProvidentFunReport", "socialmanage.socialapply": "刘夏梅 社保管理 社保申请 | ./empwelfare/Socialmanage/SocialApply", "socialmanage.SocialUpdateSelect": "刘夏梅 社保管理 社保修改 | ./empwelfare/Socialmanage/SocialUpdateSelect", "providentFund.UpdateSelect": "刘夏梅 公积金管理 公积金修改 | ./empwelfare/ProvidentFund/PfUpdateSelect", "providentFund.SocialApplySelect": "刘夏梅 公积金管理 公积金申请 | ./empwelfare/ProvidentFund/ProvidentFundApply", "socialmanage.socialProcess": "刘夏梅 社保管理 社保办理 | ./empwelfare/Socialmanage/SocialProcess/index", "socialmanage.ssacct": "刘夏梅 社保管理 社保账号管理 | ./empwelfare/Socialmanage/Ssacct", "socialmanage.QuerySsAdjustment": "赵煜颢 社保调整 | ./empwelfare/Socialmanage/QuerySsAdjustment", "socialmanage.QuerySsMinBaseAdjustment": "赵煜颢 社保最低基数调整 | ./empwelfare/Socialmanage/QuerySsMinBaseAdjustment", "providentFund.QueryPfAdjustment": "赵煜颢 公积金调整 | ./empwelfare/ProvidentFund/QueryPfAdjustment", "providentFund.QueryPfMinBaseAdjustment": "赵煜颢 公积金最低基数调整 | ./empwelfare/ProvidentFund/QueryPfMinBaseAdjustment", "providentFund.ssacct": "刘夏梅 公积金管理 公积金账号管理 | ./empwelfare/ProvidentFund/Ssacct", "socialmanage.SocialPay": "赵煜灏 社保管理 社保支付 | ./empwelfare/PayManage/SocialPay", "providentFund.ProvidentFundPay": "赵煜灏 公积金管理 公积金支付 | ./empwelfare/PayManage/ProvidentFundPay", "socialmanage.ssImpBatch": "刘夏梅 社保管理 批量导入社保账号 | ./empwelfare/Socialmanage/QuerySSImpBatch", "providentFund.ssImpBatch": "刘夏梅 公积金管理 批量导入公积金账号 | ./empwelfare/ProvidentFund/QuerySSImpBatch", "socialmanage.socialSecurity": "刘夏梅 社保管理 社保查询 | ./empwelfare/Socialmanage/QuerySocialSecurity", "providentFund.socialSecurity": "刘夏梅 公积金管理 公积金查询 | ./empwelfare/ProvidentFund/QuerySocialSecurity", "socialmanage.socialMakeUp": "刘夏梅 社保管理 社保补缴查询 | ./empwelfare/Socialmanage/QuerySocialMakeUp", "providentFund.socialMakeUp": "刘夏梅 公积金管理 公积金补缴查询 | ./empwelfare/ProvidentFund/QuerySocialMakeUp", "laborcontract": "刘夏梅 员工劳动合同管理 | ./empwelfare/LaborcontractManage", "providentFund.process": "刘夏梅 公积金管理 公积金办理 | ./empwelfare/ProvidentFund/ProvidentFundProcess", "socialmanage.socialStop": "刘夏梅 社保管理 社保停办 | ./empwelfare/Socialmanage/SocialStop", "intelligentSocial.socialIqGrab": "赵煜颢 社保管理 社保智能抓取增员 | ./empwelfare/Socialmanage/SocialIqGrab", "intelligentSocial.socialIqGrabReduce": "刘夏梅 社保管理 社保智能抓取减员 | ./empwelfare/Socialmanage/SocialIqGrabReduce", "intelligentSocial.socialBatchQuery": "赵煜颢 社保管理 社保反馈批次查询 | ./empwelfare/Socialmanage/SocialBatchQuery", "intelligentSocial.socialDetailedQuery": "赵煜颢 社保管理 社保反馈明细查询 | ./empwelfare/Socialmanage/SocialDetailedQuery", "providentFund.fundStop": "刘夏梅 公积金管理 公积金停办 | ./empwelfare/ProvidentFund/ProvidentFundStop", "socialmanage.SocialChangeSelect": "刘夏梅 社保管理 社保变更 | ./empwelfare/Socialmanage/SocialChange", "providentFund.ChangeSelect": "刘夏梅 公积金管理 公积金变更 | ./empwelfare/ProvidentFund/PfChange", "ebmtransact.simpleBusiness.SimpleImpInterface": "严小强 简易业务办理 简易导入接口设置 | ./empwelfare/Ebmtransact/SimpleBusiness/SimpleImpInterface", "ebmtransact.simpleBusiness.SimpleBusinessTransact": "严小强 简易业务办理 简易业务批量办理 | ./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessTransact", "ebmtransact.simpleBusiness.SimpleBusinessQuery": "严小强 简易业务办理 简易业务查询 | ./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessQuery", "ebmtransact.EbmTransactQuery": "赵煜颢 简易业务办理 员工业务查询 | ./empwelfare/Ebmtransact/EbmTransactQuery", "ebmtransact.HospitalTransact": "赵煜颢 简易业务办理 定点医疗机构变更 | ./empwelfare/Ebmtransact/HospitalTransact", "ebmtransact.QueryEmpBizBooking": "刘夏梅 员工业务办理 员工预约业务办理 | ./empwelfare/Ebmtransact/QueryEmpBizBooking", "ebmtransact.SocialTransact": "刘夏梅 员工业务办理 社保业务办理 | ./empwelfare/Ebmtransact/SocialTransact", "ebmtransact.PorvidentTransact": "刘夏梅 员工业务办理 公积金业务办理 | ./empwelfare/Ebmtransact/PorvidentTransact", "ebmtransact.OnTransact": "刘夏梅 员工业务办理 在职业务办理 | ./empwelfare/Ebmtransact/OnTransact", "socialmanage.QuerySIBackPayImp": "陈国祥 社保管理->社保补缴导入 | ./empwelfare/Socialmanage/QuerySIBackPayImp", "socialmanage.AdditionalSiQuery": "陈国祥 社保管理->社保补缴滞纳金导入 | ./empwelfare/Socialmanage/AdditionalSiQuery", "providentFund.QuerySIBackPayImp": "陈国祥 公积金管理 公积金补缴导入 | ./empwelfare/ProvidentFund/QuerySIBackPayImp", "providentFund.AdditionalSiQuery": "陈国祥 公积金管理 公积金补缴滞纳金导入 | ./empwelfare/ProvidentFund/AdditionalSiQuery", "socialmanage.QuerySsAndFundPayDetail": "刘夏梅 公积金管理 社保/公积金支付明细查询 | ./empwelfare/Socialmanage/QuerySsAndFundPayDetail", "socialmanage.socialBudget": "刘夏梅 公积金管理 社保/公积金支付明细查询 | ./empwelfare/Socialmanage/BudgetQuery", "filemanagement.QueryFileManagement": "赵煜灏 员工档案管理 档案管理 | ./empwelfare/filemanagement/QueryFileManagement", "filemanagement.FilePay": "赵煜灏 员工档案管理 档案支付 | ./empwelfare/FilePay", "sbgjtjsj.SBGJJTJ": "刘夏梅 社保公积金调基收集 社保公积金调基 | ./empwelfare/sbgjtjsj/SBGJJTJ", "sbgjtjsj.GYSSBGJJTJ": "刘夏梅 社保公积金调基收集 供应商社保公积金调基 | ./empwelfare/sbgjtjsj/GYSSBGJJTJ", "sbgjtjsj.SBGJJTJSJSJ": "刘夏梅 社保公积金调基收集数据 | ./empwelfare/sbgjtjsj/CollectSSAdjust", "sbgjtjsj.SBGJJTJSP": "王正荣 社保公积金调基审批 | ./empwelfare/sbgjtjsj/SBGJJTJSP", "dockingManagement.employeeCertificateManagement": "严小强 华住对接管理 华住员工证件管理 | ./empwelfare/dockingManagement/EmployeeCertificateManagement", "dockingManagement.laborcontractManagement": "刘夏梅 华住对接管理 华住劳动合同管理 | ./empwelfare/dockingManagement/LaborcontractManagement", "dockingManagement.personnelDataManagement": "刘夏梅 华住对接管理 华住人员数据管理 | ./empwelfare/dockingManagement/PersonnelDataManagement", "uploadLaborcontract": "刘夏梅 劳动合同附件上传 | ./empwelfare/UploadLaborcontract", "exportLaborcontract": "赵煜颢 导出电子合同详情 | ./empwelfare/ExportLaborcontract", "resignationMaterials": "刘夏梅 员工离职材料管理 | ./empwelfare/ResignationMaterials", "resignationMaterialsUpload": "刘夏梅 离职材料附件上传 | ./empwelfare/ResignationMaterialsUpload", "socialmanage.socialSecurityUnitNo": "刘夏梅 社保管理 社保单位编号 | ./empwelfare/Socialmanage/SocialSecurityUnitNo", "intelligentSocial.socialInsuranceExportRules": "刘夏梅 社保管理 社保自动导出规则维护 | ./empwelfare/Socialmanage/SocialInsuranceExportRules", "laborContractRenewalConfirmation": "刘夏梅 劳动合同续签确认(EOS提交) | ./empwelfare/LaborContractRenewalConfirmation", "customerorderForLaborContract": "刘夏梅 大客户续签劳动合同 | ./empwelfare/CustomerorderForLaborContract", "electronicBusiness": "刘夏梅 电子业务办理 | ./empwelfare/ElectronicBusiness", "intelligentSocial.automaticTaxReport": "刘夏梅 智能社保 税务自动报送 | ./empwelfare/Socialmanage/AutomaticTaxReport", "intelligentSocial.socialCheck": "刘夏梅 智能社保 社保反查 | ./empwelfare/IntelligentSocial/SocialCheck", "retiremanage.retirequery": "谭金晶 退休查询 | ./empwelfare/RetireManage/RetireQuery", "retiremanage.ClientSide": "孙尚阳 退休管理 客户端退休 | ./empwelfare/RetireManage/ClientSide", "retiremanage.DataCollect": "孙尚阳 退休管理 退休材料收集 | ./empwelfare/RetireManage/DataCollect", "retiremanage.Manage": "孙尚阳 退休管理 退休办理 | ./empwelfare/RetireManage/Manage", "retiremanage.BillConfirm": "孙尚阳 退休管理 确认退休订单 | ./empwelfare/RetireManage/BillConfirm", "retiremanage.Query": "孙尚阳 退休管理 退休办理查询 | ./empwelfare/RetireManage/Query", "intelligentSocial.CMSCode": "孙尚阳 智能社保 手机验证码获取 | ./empwelfare/IntelligentSocial/CMSCode", "intelligentSocial.processConfiguration": "刘夏梅 智能社保 流程配置列表 | ./empwelfare/IntelligentSocial/ProcessConfiguration", "intelligentSocial.taxProcessConfig": "孙尚阳 智能社保 税务流程配置 | ./empwelfare/IntelligentSocial/TaxProcessConfig", "intelligentSocial.companyFeeRes": "孙尚阳 智能社保 企业缴费结果查询 | ./empwelfare/IntelligentSocial/CompanyFeeRes", "intelligentSocial.empFeeRes": "孙尚阳 智能社保 员工缴费结果查询 | ./empwelfare/IntelligentSocial/EmpFeeRes", "intelligentSocial.ForceRelate": "孙尚阳 智能社保 入职强制关联 | ./empwelfare/IntelligentSocial/ForceRelate", "intelligentSocial.SecondaryBatchImport": "赵煜颢 智能社保 二级户批量导入 | ./empwelfare/IntelligentSocial/SecondaryBatchImport", "intelligentSocial.socialcertificate.batchQuery": "刘夏梅 社保凭证 社保凭证批次查询  | ./empwelfare/IntelligentSocial/socialcertificate/BatchQuery", "intelligentSocial.socialcertificate.printEmployee": "刘夏梅 社保凭证 社保凭证（按员工单个打印） | ./empwelfare/IntelligentSocial/socialcertificate/PrintEmployee", "intelligentSocial.socialcertificate.printHousehold": "刘夏梅 社保凭证 社保凭证（按户打印） | ./empwelfare/IntelligentSocial/socialcertificate/PrintHousehold", "intelligentSocial.taxComparisonFailed": "刘夏梅 智能社保 税务比对失败员工名单 | ./empwelfare/IntelligentSocial/TaxComparisonFailed", "intelligentSocial.socialcertificate.printEmployeeBatch": "刘夏梅 社保凭证 社保凭证（按员工单个打印） | ./empwelfare/IntelligentSocial/socialcertificate/PrintEmployeeBatch"}