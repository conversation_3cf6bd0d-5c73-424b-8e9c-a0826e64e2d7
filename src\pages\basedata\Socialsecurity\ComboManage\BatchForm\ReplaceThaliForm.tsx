import React, { useEffect, useState } from 'react';
import Codal from '@/components/Codal';
import { Form, Button, Input, Col, Row } from 'antd';
import { EnumerateFields, EditeFormProps } from '@/components/CachedPage/EnumerateFields';
import { FormElement3, RowElement, ColElementButton } from '@/components/Forms/FormLayouts';
import { StandardTableColumnProps } from '@/components/StandardTable';
import ConfirmButton, { ConfirmLoading } from '@/components/Forms/Confirm';
import { msgErr, msgOk, resError } from '@/utils/methods/message';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { useWritable, Writable } from '@/components/Writable';
import FormItem from 'antd/lib/form/FormItem';
import { ColElement } from '@/components/Forms/FormLayoutFour';
import { SsComboPop } from '@/components/StandardPop/SsComboPop';

interface ReplaceThaliFormProps {
  title: string;
  visible: boolean;
  hideHandle: CallableFunction;
  detailData: POJO;
}
const columns: StandardTableColumnProps<any>[] = [
  { title: '客户编号', dataIndex: 'custCode' },
  { title: '客户名称', dataIndex: 'custName' },
  { title: '接单分公司ID', dataIndex: 'assigneeProvider', hidden: true },
  { title: '接单分公司', dataIndex: 'assigneeProvider' },
  { title: '小合同编号', dataIndex: 'subcontractId' },
  { title: '小合同名称', dataIndex: 'subcontractName' },
  { title: '小合同状态', dataIndex: 'stateName' },
];

const service = API.emphiresep.subcontract.querySubcontractList;
const serviceReplace = API.combo.comboHro.replaceSubContractCombo;
const ReplaceThaliForm: React.FC<ReplaceThaliFormProps> = (props) => {
  const { title, visible, hideHandle, detailData } = props;
  if (!visible) return null;
  const [form] = Form.useForm();
  const [positionIndex, setPositionIndex] = useState<number | undefined>(undefined);
  const wriTable = useWritable({ service, cached: false });
  const [searchKey, setSearchKey] = useState<string>('');

  const [selectedRows, setSelectedRows] = useState<Array<any>>([]);

  const [isAllowNewSsCombo, setIsAllowNewSsCombo] = useState<boolean>(false);

  const formColumns: EditeFormProps[] = [
    {
      label: '原社保套餐',
      fieldName: 'ssComboNameOld',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => {
        return (
          <CustomerPop
            rowValue="custId-custName"
            keyMap={{
              custId: 'custId',
              custName: 'custName',
            }}
          />
        );
      },
    },
  ];

  const formColumnsNew: EditeFormProps[] = [
    {
      label: '新社保套餐',
      fieldName: 'comboId',
      inputProps: {},
      inputRender: () => {
        return (
          <SsComboPop
            disabled={!isAllowNewSsCombo}
            rowValue="comboId-ssComboName"
            keyMap={{
              comboId: 'comboId',
              ssComboName: 'ssComboName',
            }}
            fixedValues={{
              branchId: selectedRows?.[0]?.assigneeProviderId,
              branchName: selectedRows?.[0]?.assigneeProvider,
              personCategoryId: detailData?.personCategoryId,
              isIndependent: detailData?.isIndependent,
            }}
          />
        );
      },
      rules: [{ required: true, message: '请选择新社保套餐' }],
    },
  ];

  const searchList = () => {
    wriTable.request({ ...wriTable?.queries, comboId: detailData?.comboId });
  };

  const oKhandle = () => {
    form.validateFields().then(() => {
      const data: Array<any> = selectedRows?.map((item) => {
        return { ...item, comboId: form.getFieldValue('comboId') };
      });

      serviceReplace.request(data).then((res) => {
        if (resError(res)) {
          msgErr(res.message);
          return;
        }
        msgOk('替换成功');
        hideHandle();
      });
    });
    ConfirmLoading.clearLoading(serviceReplace);
  };

  const onPosition = () => {
    const position = wriTable.searchRows(searchKey, ['subcontractId']);
    if (position !== undefined && position > -1) {
      setPositionIndex(position);
      setTimeout(() => {
        setPositionIndex(undefined);
      }, 1000);
    } else {
      msgErr('未查找到数据');
    }
  };

  const renderFooter = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <ConfirmButton
            service={serviceReplace}
            key="saveAll"
            type="primary"
            onClick={() => oKhandle()}
          >
            确认
          </ConfirmButton>
          <Button key="cancel" onClick={() => hideHandle()}>
            取消
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };

  useEffect(() => {
    if (!visible) return;
    const { ssComboName, ...rest } = detailData;
    detailData && form.setFieldsValue({ ...rest, ssComboNameOld: detailData.ssComboName });
  }, [visible]);

  return (
    <Codal
      destroyOnClose
      width={1200}
      title={title}
      visible={visible}
      footer={renderFooter()}
      onCancel={() => hideHandle()}
    >
      <FormElement3 form={form} preserve={false}>
        <Row>
          <Col span={18}>
            <EnumerateFields outerForm={form} formColumns={formColumns} colNumber={2} />
          </Col>
          <Col span={6}>
            <Button onClick={searchList}>查询</Button>
          </Col>
        </Row>
        <RowElement>
          <ColElement>
            <FormItem label="小合同编号" labelCol={{ span: 9 }}>
              <Input value={searchKey} onChange={(e) => setSearchKey(e.target.value)} />
            </FormItem>
          </ColElement>
          <ColElement>
            <Button onClick={onPosition}>定位</Button>
          </ColElement>
        </RowElement>
        <Writable
          service={service}
          rowKey="subcontractId"
          columns={columns}
          positionIndex={positionIndex}
          notShowPagination
          wriTable={wriTable}
          selectedRows={selectedRows}
          onSelectRow={(rows: Array<any>) => {
            setSelectedRows(rows);
            setIsAllowNewSsCombo(false);

            if (rows.length > 0) {
              if (new Set(rows.map((m) => m?.assigneeProviderId) || []).size === 1) {
                setIsAllowNewSsCombo(true);
              } else {
                setIsAllowNewSsCombo(false);
                msgErr('选择的小合同接单分公司不同，不能批量处理');
              }
            }
          }}
        />
        <EnumerateFields outerForm={form} formColumns={formColumnsNew} colNumber={3} />
      </FormElement3>
    </Codal>
  );
};
export default ReplaceThaliForm;
