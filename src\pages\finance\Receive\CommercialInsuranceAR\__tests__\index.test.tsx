import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Form } from 'antd';
import '@testing-library/jest-dom';
import CommercialInsuranceAR from '../index';

// Mock dependencies
jest.mock('@/components/CachedPage', () => {
  return {
    CachedPage: ({ formColumns, columns, form }: any) => (
      <div data-testid="cached-page">
        <Form form={form}>
          {formColumns.map((col: any, index: number) => (
            <div key={index} data-testid={`form-field-${col.fieldName || index}`}>
              <label>{col.label}</label>
              {typeof col.inputRender === 'function' ? col.inputRender() : col.inputRender}
            </div>
          ))}
        </Form>
        <div data-testid="table-columns">
          {columns.map((col: any, index: number) => (
            <div key={index} data-testid={`column-${col.dataIndex}`}>
              {col.title}
            </div>
          ))}
        </div>
      </div>
    ),
  };
});

jest.mock('../BillDetailModal', () => ({
  BillDetailModal: () => <div data-testid="bill-detail-modal" />,
}));

jest.mock('@/pages/Sales/Quotation/QuotationTempManage/NumericInput', () => {
  return function NumericInput(props: any) {
    return (
      <input
        data-testid="numeric-input"
        placeholder={props.placeholder}
        onChange={(e) => props.onValueChange?.(Number(e.target.value))}
        {...props}
      />
    );
  };
});

jest.mock('@/utils/methods/format', () => ({
  formatAmount: (value: string) => `¥${value}`,
}));

// Mock other dependencies
jest.mock('@/components/StandardPop/CustomerPop', () => ({
  CustomerPop: () => <div data-testid="customer-pop" />,
}));

jest.mock('@/components/DateRange4', () => ({
  DateRange: () => <div data-testid="date-range" />,
}));

jest.mock('../QueryBill/components/BillCreatorSubSelectPop', () => {
  return function BillCreatorSubSelectPop() {
    return <div data-testid="bill-creator-select" />;
  };
});

jest.mock('@/components/Selectors/FuncSelectors', () => ({
  mapToSelectors: () => <div data-testid="selector" />,
}));

describe('CommercialInsuranceAR', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<CommercialInsuranceAR />);
    expect(screen.getByTestId('cached-page')).toBeInTheDocument();
  });

  it('renders receivable amount fields with NumericInput', () => {
    render(<CommercialInsuranceAR />);
    
    // Check if receivable amount start field exists
    expect(screen.getByTestId('form-field-receivableAmtSt')).toBeInTheDocument();
    expect(screen.getByText('应收金额>=')).toBeInTheDocument();
    
    // Check if receivable amount end field exists
    expect(screen.getByTestId('form-field-receivableAmtEd')).toBeInTheDocument();
    expect(screen.getByText('应收金额<=')).toBeInTheDocument();
    
    // Check if NumericInput components are rendered
    const numericInputs = screen.getAllByTestId('numeric-input');
    expect(numericInputs).toHaveLength(2);
  });

  it('displays receivable amount column in table', () => {
    render(<CommercialInsuranceAR />);
    
    // Check if receivable amount column exists in table
    expect(screen.getByTestId('column-receivableAmt')).toBeInTheDocument();
    expect(screen.getByText('应收金额')).toBeInTheDocument();
  });

  it('formats amount values correctly', () => {
    const { formatAmount } = require('@/utils/methods/format');
    
    // Test amount formatting
    expect(formatAmount('1234.56')).toBe('¥1234.56');
    expect(formatAmount('0.00')).toBe('¥0.00');
  });

  it('validates numeric input correctly', async () => {
    const TestForm = () => {
      const [form] = Form.useForm();
      
      const validateReceivableAmount = async (value: any) => {
        if (!value) return Promise.resolve();
        const numValue = Number(value);
        if (isNaN(numValue) || numValue < 0) {
          return Promise.reject(new Error('请输入有效的应收金额数字'));
        }
        return Promise.resolve();
      };

      return (
        <Form form={form}>
          <Form.Item
            name="receivableAmtSt"
            rules={[{ validator: (_, value) => validateReceivableAmount(value) }]}
          >
            <input data-testid="test-input" />
          </Form.Item>
        </Form>
      );
    };

    render(<TestForm />);
    
    const input = screen.getByTestId('test-input');
    
    // Test valid input
    fireEvent.change(input, { target: { value: '100.50' } });
    await waitFor(() => {
      expect(input).toHaveValue('100.50');
    });
    
    // Test invalid input (negative)
    fireEvent.change(input, { target: { value: '-50' } });
    // Note: Full validation testing would require more complex setup with form validation
  });

  it('handles empty or null amount values in table render', () => {
    // Test the render function logic for amount column
    const mockRender = (value: any) => {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return value;
      }
      const { formatAmount } = require('@/utils/methods/format');
      return formatAmount(numValue.toFixed(2));
    };

    expect(mockRender(null)).toBe('-');
    expect(mockRender(undefined)).toBe('-');
    expect(mockRender('')).toBe('-');
    expect(mockRender('invalid')).toBe('invalid');
    expect(mockRender(123.456)).toBe('¥123.46');
    expect(mockRender('100.5')).toBe('¥100.50');
  });
});
