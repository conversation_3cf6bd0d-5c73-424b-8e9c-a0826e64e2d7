import React from 'react';
import { WritableColumnProps } from '@/utils/writable/types';
import Codal from '@/components/Codal';
import StandardTable from '@/components/StandardTable';

const columns: WritableColumnProps<any>[] = [
  { title: '产品大类', dataIndex: 'activityNameCn' },
  { title: '产品金额', dataIndex: 'status' },
  { title: '产品企业金额', dataIndex: 'createDt1' },
  { title: '产品个人金额', dataIndex: 'endTime1' },
  { title: '产品增值税率', dataIndex: 'participantName' },
  { title: '产品增值税费', dataIndex: 'approvalOpinion' },
  { title: '人数', dataIndex: '1' },
  { title: '签单分公司抬头', dataIndex: '2' },
];

interface BillDetailModalProps {
  modal: [boolean, CallableFunction];
  processInstanceId?: string;
}

const BillDetailModal: React.FC<BillDetailModalProps> = (props) => {
  const { modal } = props;
  const [visible, setVisible] = modal;
  if (!visible) return null;
  const [table, tableOptions] = StandardTable.useStandardTable({
    service: API.finance.receivable.getLogList,
  });

  return (
    <Codal
      title="商保账单应收明细"
      visible={visible}
      onCancel={() => setVisible(false)}
      onOk={() => setVisible(false)}
      footer={null}
      width="80%"
    >
      <StandardTable columns={columns} notShowPagination {...(table as any)} />
    </Codal>
  );
};

export { BillDetailModal };
